export interface User {
  id: string;
  name: string;
  email: string;
  phone?: string;
  isAdmin: boolean;
  uid?: string; // Firebase UID
}

export interface Category {
  id: string;
  name: string;
  description: string;
  isActive: boolean;
  candidates: Candidate[];
}

export interface Candidate {
  id: string;
  name: string;
  description: string;
  image?: string;
  votes: number;
}

export interface Vote {
  id: string;
  userId: string;
  categoryId: string;
  candidateId: string;
  timestamp: Date;
}

export interface AuthContextType {
  user: User | null;
  sendSMSCode: (phone: string) => Promise<boolean>;
  verifyCode: (code: string, name?: string) => Promise<boolean>;
  adminLogin: (password: string) => Promise<boolean>;
  logout: () => void;
  isLoading: boolean;
  updateProfile: (updates: Partial<User>) => Promise<boolean>;
  initRecaptcha: (elementId: string) => void;
}

export interface VotingContextType {
  categories: Category[];
  votes: Vote[];
  addCategory: (category: Omit<Category, "id">) => void;
  updateCategory: (id: string, category: Partial<Category>) => void;
  deleteCategory: (id: string) => void;
  addCandidate: (
    categoryId: string,
    candidate: Omit<Candidate, "id" | "votes">
  ) => void;
  updateCandidate: (
    categoryId: string,
    candidateId: string,
    candidate: Partial<Candidate>
  ) => void;
  deleteCandidate: (categoryId: string, candidateId: string) => void;
  submitVote: (categoryId: string, candidateId: string) => boolean;
  getUserVotes: (userId: string) => Vote[];
  getResults: () => Category[];
  exportResults: () => void;
}
