import { LogOut, <PERSON>u, <PERSON><PERSON><PERSON>, <PERSON>, User } from "lucide-react";
import React, { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";

const Header: React.FC = () => {
  const { user, logout } = useAuth();
  const location = useLocation();
  const [drawerOpen, setDrawerOpen] = useState(false);

  const isActive = (path: string) => location.pathname === path;

  const navLinks = (
    <>
      <Link
        to="/"
        className={`font-medium transition-colors duration-200 hover:text-primary-600 ${
          isActive("/")
            ? "text-primary-600 border-b-2 border-primary-500 pb-1"
            : "text-secondary-800"
        }`}
        onClick={() => setDrawerOpen(false)}
      >
        Início
      </Link>
      <Link
        to="/voting"
        className={`font-medium transition-colors duration-200 hover:text-primary-600 ${
          isActive("/voting")
            ? "text-primary-600 border-b-2 border-primary-500 pb-1"
            : "text-secondary-800"
        }`}
        onClick={() => setDrawerOpen(false)}
      >
        Votações
      </Link>
      {user ? (
        <>
          {!user.isAdmin && (
            <Link
              to="/profile"
              className={`font-medium transition-colors duration-200 hover:text-primary-600 ${
                isActive("/profile")
                  ? "text-primary-600 border-b-2 border-primary-500 pb-1"
                  : "text-secondary-800"
              }`}
              onClick={() => setDrawerOpen(false)}
            >
              Perfil
            </Link>
          )}
          {user.isAdmin && (
            <Link
              to="/admin"
              className={`font-medium transition-colors duration-200 hover:text-primary-600 ${
                isActive("/admin")
                  ? "text-primary-600 border-b-2 border-primary-500 pb-1"
                  : "text-secondary-800"
              }`}
              onClick={() => setDrawerOpen(false)}
            >
              <div className="flex items-center space-x-1">
                <Settings className="h-4 w-4" />
                <span>Admin</span>
              </div>
            </Link>
          )}
        </>
      ) : null}
    </>
  );

  return (
    <header className="bg-white shadow-md border-b-4 border-primary-500">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4">
          <Link to="/" className="flex items-center space-x-3 group">
            <div className="p-2 bg-primary-500 rounded-full group-hover:shadow-lg transition-all duration-300">
              <Trophy className="h-8 w-8 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-secondary-900">
                Troféu irmão dantas
              </h1>
            </div>
          </Link>
          <span className="hidden md:block text-secondary-500"></span>
          <span className="hidden md:block text-secondary-500"></span>
          <span className="hidden md:block text-secondary-500"></span>
          <span className="hidden md:block text-secondary-500"></span>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {navLinks}
          </nav>

          {/* Desktop Entrar/Usuário */}
          <div className="hidden md:flex items-center space-x-4">
            {user ? (
              <div className="flex items-center space-x-3">
                <div className="flex flex-col items-end">
                  <span className="font-medium text-secondary-900">
                    {user.name || "Usuário"}
                  </span>
                  <span className="text-xs text-secondary-500">
                    {user.isAdmin ? "Administrador" : "Eleitor"}
                  </span>
                </div>
                <button
                  onClick={logout}
                  className="p-2 text-secondary-400 hover:text-primary-600 hover:bg-primary-50 rounded-full transition-colors duration-200"
                  title="Sair"
                >
                  <LogOut className="h-5 w-5" />
                </button>
              </div>
            ) : (
              <Link
                to="/login"
                className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-500 hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Entrar
              </Link>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setDrawerOpen(!drawerOpen)}
              className="p-2 text-secondary-400 hover:text-primary-600 hover:bg-primary-50 rounded-full transition-colors duration-200"
            >
              <Menu className="h-6 w-6" />
            </button>
          </div>
        </div>
      </div>

      {/* Mobile drawer */}
      {drawerOpen && (
        <div className="fixed inset-0 z-50 md:hidden">
          {/* Overlay */}
          <div
            className="fixed inset-0 bg-black bg-opacity-40"
            onClick={() => setDrawerOpen(false)}
          ></div>

          {/* Side drawer */}
          <div className="fixed inset-y-0 right-0 w-72 bg-white shadow-xl flex flex-col">
            <div className="px-4 py-6 border-b border-secondary-100 flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Trophy className="h-6 w-6 text-primary-500" />
                <h2 className="text-lg font-semibold text-secondary-900">
                  Menu
                </h2>
              </div>
              <button
                onClick={() => setDrawerOpen(false)}
                className="p-2 text-secondary-400 hover:text-primary-600 hover:bg-primary-50 rounded-full transition-colors duration-200"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>

            <div className="flex-1 overflow-auto">
              <nav className="flex flex-col space-y-1 p-4">{navLinks}</nav>
            </div>

            <div className="p-4 border-t border-secondary-100">
              {user ? (
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                      <User className="h-5 w-5 text-primary-600" />
                    </div>
                    <div>
                      <div className="font-medium text-secondary-900">
                        {user.name || "Usuário"}
                      </div>
                      <div className="text-xs text-secondary-500">
                        {user.isAdmin ? "Administrador" : "Eleitor"}
                      </div>
                    </div>
                  </div>
                  <button
                    onClick={logout}
                    className="p-2 text-secondary-400 hover:text-primary-600 hover:bg-primary-50 rounded-full transition-colors duration-200"
                    title="Sair"
                  >
                    <LogOut className="h-5 w-5" />
                  </button>
                </div>
              ) : (
                <Link
                  to="/login"
                  onClick={() => setDrawerOpen(false)}
                  className="flex items-center justify-center w-full px-4 py-2 bg-primary-500 text-white rounded-md hover:bg-primary-600 transition-colors duration-200"
                >
                  <User className="h-5 w-5 mr-2" />
                  Entrar
                </Link>
              )}
            </div>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
