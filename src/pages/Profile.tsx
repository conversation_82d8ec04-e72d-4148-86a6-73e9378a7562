import React from 'react';
import { User, Phone, CheckCircle, Calendar, Trophy } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { useVoting } from '../contexts/VotingContext';

const Profile: React.FC = () => {
  const { user } = useAuth();
  const { categories, getUserVotes } = useVoting();

  if (!user) return null;

  const userVotes = getUserVotes(user.id);
  const activeCategories = categories.filter(cat => cat.isActive);
  const votedCategories = userVotes.length;
  const totalCategories = activeCategories.length;

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-yellow-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-yellow-600 to-purple-600 bg-clip-text text-transparent mb-4">
            <PERSON>u <PERSON>
          </h1>
          <p className="text-xl text-gray-600">
            Suas informações e histórico de votações
          </p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* User Info Card */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
              <div className="bg-gradient-to-r from-yellow-400 to-purple-600 px-6 py-8 text-center">
                <div className="w-20 h-20 bg-white rounded-full flex items-center justify-center mx-auto mb-4">
                  <User className="h-10 w-10 text-purple-600" />
                </div>
                <h2 className="text-xl font-bold text-white mb-1">{user.name}</h2>
                <p className="text-white/90">Participante</p>
              </div>
              
              <div className="p-6 space-y-4">
                <div className="flex items-center space-x-3">
                  <Phone className="h-5 w-5 text-gray-400" />
                  <span className="text-gray-700">{user.phone}</span>
                </div>
                
                <div className="flex items-center space-x-3">
                  <Calendar className="h-5 w-5 text-gray-400" />
                  <span className="text-gray-700">Membro desde hoje</span>
                </div>
              </div>
            </div>

            {/* Stats Card */}
            <div className="bg-white rounded-2xl shadow-xl p-6 mt-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Estatísticas</h3>
              
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Votações Realizadas</span>
                  <span className="text-2xl font-bold text-purple-600">{votedCategories}</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Total de Categorias</span>
                  <span className="text-2xl font-bold text-gray-900">{totalCategories}</span>
                </div>
                
                <div className="pt-2">
                  <div className="flex justify-between text-sm text-gray-600 mb-1">
                    <span>Progresso</span>
                    <span>{totalCategories > 0 ? Math.round((votedCategories / totalCategories) * 100) : 0}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-gradient-to-r from-yellow-500 to-purple-600 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${totalCategories > 0 ? (votedCategories / totalCategories) * 100 : 0}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Voting History */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
              <div className="bg-gradient-to-r from-purple-600 to-blue-600 px-6 py-4">
                <h2 className="text-xl font-bold text-white flex items-center space-x-2">
                  <Trophy className="h-6 w-6" />
                  <span>Histórico de Votações</span>
                </h2>
              </div>

              <div className="p-6">
                {userVotes.length > 0 ? (
                  <div className="space-y-4">
                    {userVotes.map(vote => {
                      const category = categories.find(cat => cat.id === vote.categoryId);
                      const candidate = category?.candidates.find(cand => cand.id === vote.candidateId);
                      
                      if (!category || !candidate) return null;

                      return (
                        <div key={vote.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
                          <div className="flex items-center space-x-4">
                            <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                              <CheckCircle className="h-6 w-6 text-green-600" />
                            </div>
                            <div>
                              <h3 className="font-semibold text-gray-900">{category.name}</h3>
                              <p className="text-gray-600">Votou em: {candidate.name}</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="text-sm text-gray-500">
                              {new Date(vote.timestamp).toLocaleDateString('pt-BR')}
                            </p>
                            <p className="text-sm text-gray-500">
                              {new Date(vote.timestamp).toLocaleTimeString('pt-BR')}
                            </p>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <Trophy className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Nenhuma votação realizada</h3>
                    <p className="text-gray-600 mb-6">
                      Você ainda não votou em nenhuma categoria. Que tal começar agora?
                    </p>
                    <a
                      href="/voting"
                      className="inline-flex items-center space-x-2 bg-gradient-to-r from-yellow-500 to-purple-600 text-white px-6 py-3 rounded-full font-semibold hover:shadow-lg transition-all duration-300 transform hover:-translate-y-0.5"
                    >
                      <span>Ir para Votações</span>
                    </a>
                  </div>
                )}
              </div>
            </div>

            {/* Categories Status */}
            {totalCategories > 0 && (
              <div className="bg-white rounded-2xl shadow-xl p-6 mt-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Status das Categorias</h3>
                
                <div className="grid sm:grid-cols-2 gap-4">
                  {activeCategories.map(category => {
                    const hasVoted = userVotes.some(vote => vote.categoryId === category.id);
                    
                    return (
                      <div
                        key={category.id}
                        className={`p-4 rounded-xl border-2 ${
                          hasVoted 
                            ? 'border-green-200 bg-green-50' 
                            : 'border-gray-200 bg-gray-50'
                        }`}
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="font-medium text-gray-900">{category.name}</h4>
                            <p className="text-sm text-gray-600">{category.candidates.length} candidatos</p>
                          </div>
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                            hasVoted ? 'bg-green-200' : 'bg-gray-200'
                          }`}>
                            {hasVoted ? (
                              <CheckCircle className="h-5 w-5 text-green-600" />
                            ) : (
                              <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;