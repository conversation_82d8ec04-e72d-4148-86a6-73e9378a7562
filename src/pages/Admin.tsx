import React, { useState } from 'react';
import { Plus, Edit, Trash2, Users, BarChart3, Download, Settings, Eye, EyeOff } from 'lucide-react';
import { useVoting } from '../contexts/VotingContext';
import { Category, Candidate } from '../types';

const Admin: React.FC = () => {
  const {
    categories,
    addCategory,
    updateCategory,
    deleteCategory,
    addCandidate,
    updateCandidate,
    deleteCandidate,
    exportResults
  } = useVoting();

  const [activeTab, setActiveTab] = useState<'categories' | 'results'>('categories');
  const [showModal, setShowModal] = useState<'category' | 'candidate' | null>(null);
  const [editingItem, setEditingItem] = useState<any>(null);
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>('');

  const [categoryForm, setCategoryForm] = useState({
    name: '',
    description: '',
    isActive: true
  });

  const [candidateForm, setCandidateForm] = useState({
    name: '',
    description: ''
  });

  const resetForms = () => {
    setCategoryForm({ name: '', description: '', isActive: true });
    setCandidateForm({ name: '', description: '' });
    setEditingItem(null);
    setSelectedCategoryId('');
  };

  const handleAddCategory = () => {
    if (!categoryForm.name.trim()) return;
    
    addCategory({
      name: categoryForm.name,
      description: categoryForm.description,
      isActive: categoryForm.isActive,
      candidates: []
    });
    
    resetForms();
    setShowModal(null);
  };

  const handleEditCategory = (category: Category) => {
    setCategoryForm({
      name: category.name,
      description: category.description,
      isActive: category.isActive
    });
    setEditingItem(category);
    setShowModal('category');
  };

  const handleUpdateCategory = () => {
    if (!editingItem || !categoryForm.name.trim()) return;
    
    updateCategory(editingItem.id, {
      name: categoryForm.name,
      description: categoryForm.description,
      isActive: categoryForm.isActive
    });
    
    resetForms();
    setShowModal(null);
  };

  const handleAddCandidate = () => {
    if (!candidateForm.name.trim() || !selectedCategoryId) return;
    
    addCandidate(selectedCategoryId, {
      name: candidateForm.name,
      description: candidateForm.description
    });
    
    resetForms();
    setShowModal(null);
  };

  const handleEditCandidate = (categoryId: string, candidate: Candidate) => {
    setCandidateForm({
      name: candidate.name,
      description: candidate.description
    });
    setEditingItem({ ...candidate, categoryId });
    setSelectedCategoryId(categoryId);
    setShowModal('candidate');
  };

  const handleUpdateCandidate = () => {
    if (!editingItem || !candidateForm.name.trim()) return;
    
    updateCandidate(editingItem.categoryId, editingItem.id, {
      name: candidateForm.name,
      description: candidateForm.description
    });
    
    resetForms();
    setShowModal(null);
  };

  const totalVotes = categories.reduce((total, cat) => 
    total + cat.candidates.reduce((catTotal, cand) => catTotal + cand.votes, 0), 0
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-yellow-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-yellow-600 to-purple-600 bg-clip-text text-transparent mb-4">
            Painel Administrativo
          </h1>
          <p className="text-xl text-gray-600">
            Gerencie categorias, candidatos e acompanhe os resultados
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-xl shadow-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total de Categorias</p>
                <p className="text-2xl font-bold text-gray-900">{categories.length}</p>
              </div>
              <Settings className="h-8 w-8 text-purple-600" />
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Categorias Ativas</p>
                <p className="text-2xl font-bold text-green-600">
                  {categories.filter(cat => cat.isActive).length}
                </p>
              </div>
              <Eye className="h-8 w-8 text-green-600" />
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total de Candidatos</p>
                <p className="text-2xl font-bold text-blue-600">
                  {categories.reduce((total, cat) => total + cat.candidates.length, 0)}
                </p>
              </div>
              <Users className="h-8 w-8 text-blue-600" />
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total de Votos</p>
                <p className="text-2xl font-bold text-yellow-600">{totalVotes}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-yellow-600" />
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
          <div className="border-b border-gray-200">
            <nav className="flex">
              <button
                onClick={() => setActiveTab('categories')}
                className={`py-4 px-8 font-medium ${
                  activeTab === 'categories'
                    ? 'bg-purple-50 text-purple-600 border-b-2 border-purple-600'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Gerenciar Categorias
              </button>
              <button
                onClick={() => setActiveTab('results')}
                className={`py-4 px-8 font-medium ${
                  activeTab === 'results'
                    ? 'bg-purple-50 text-purple-600 border-b-2 border-purple-600'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Resultados
              </button>
            </nav>
          </div>

          <div className="p-8">
            {activeTab === 'categories' && (
              <div>
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-2xl font-bold text-gray-900">Categorias de Votação</h2>
                  <button
                    onClick={() => {
                      resetForms();
                      setShowModal('category');
                    }}
                    className="bg-gradient-to-r from-yellow-500 to-purple-600 text-white px-6 py-2 rounded-lg font-medium hover:shadow-lg transition-all duration-300 flex items-center space-x-2"
                  >
                    <Plus className="h-5 w-5" />
                    <span>Nova Categoria</span>
                  </button>
                </div>

                <div className="space-y-6">
                  {categories.map(category => (
                    <div key={category.id} className="bg-gray-50 rounded-xl p-6 border border-gray-200">
                      <div className="flex justify-between items-start mb-4">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <h3 className="text-xl font-bold text-gray-900">{category.name}</h3>
                            <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                              category.isActive 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {category.isActive ? 'Ativa' : 'Inativa'}
                            </span>
                          </div>
                          <p className="text-gray-600 mb-4">{category.description}</p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => handleEditCategory(category)}
                            className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                            title="Editar categoria"
                          >
                            <Edit className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => deleteCategory(category.id)}
                            className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                            title="Excluir categoria"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </div>

                      <div>
                        <div className="flex justify-between items-center mb-4">
                          <h4 className="font-semibold text-gray-900">
                            Candidatos ({category.candidates.length})
                          </h4>
                          <button
                            onClick={() => {
                              resetForms();
                              setSelectedCategoryId(category.id);
                              setShowModal('candidate');
                            }}
                            className="text-purple-600 hover:text-purple-700 font-medium text-sm flex items-center space-x-1"
                          >
                            <Plus className="h-4 w-4" />
                            <span>Adicionar Candidato</span>
                          </button>
                        </div>

                        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                          {category.candidates.map(candidate => (
                            <div key={candidate.id} className="bg-white rounded-lg p-4 border border-gray-200">
                              <div className="flex justify-between items-start">
                                <div className="flex-1">
                                  <h5 className="font-medium text-gray-900 mb-1">{candidate.name}</h5>
                                  <p className="text-sm text-gray-600 mb-2">{candidate.description}</p>
                                  <p className="text-sm text-purple-600 font-medium">
                                    {candidate.votes} votos
                                  </p>
                                </div>
                                <div className="flex space-x-1">
                                  <button
                                    onClick={() => handleEditCandidate(category.id, candidate)}
                                    className="p-1 text-blue-600 hover:bg-blue-50 rounded"
                                    title="Editar candidato"
                                  >
                                    <Edit className="h-3 w-3" />
                                  </button>
                                  <button
                                    onClick={() => deleteCandidate(category.id, candidate.id)}
                                    className="p-1 text-red-600 hover:bg-red-50 rounded"
                                    title="Excluir candidato"
                                  >
                                    <Trash2 className="h-3 w-3" />
                                  </button>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'results' && (
              <div>
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-2xl font-bold text-gray-900">Resultados das Votações</h2>
                  <button
                    onClick={exportResults}
                    className="bg-gradient-to-r from-green-500 to-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:shadow-lg transition-all duration-300 flex items-center space-x-2"
                  >
                    <Download className="h-5 w-5" />
                    <span>Exportar Resultados</span>
                  </button>
                </div>

                <div className="space-y-8">
                  {categories.map(category => (
                    <div key={category.id} className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                      <div className="flex justify-between items-center mb-6">
                        <h3 className="text-xl font-bold text-gray-900">{category.name}</h3>
                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                          category.isActive 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {category.isActive ? 'Ativa' : 'Encerrada'}
                        </span>
                      </div>

                      <div className="space-y-4">
                        {category.candidates
                          .sort((a, b) => b.votes - a.votes)
                          .map((candidate, index) => {
                            const totalCategoryVotes = category.candidates.reduce((sum, c) => sum + c.votes, 0);
                            const percentage = totalCategoryVotes > 0 ? (candidate.votes / totalCategoryVotes) * 100 : 0;
                            
                            return (
                              <div key={candidate.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                <div className="flex items-center space-x-4">
                                  <div className={`w-8 h-8 rounded-full flex items-center justify-center font-bold text-white ${
                                    index === 0 ? 'bg-yellow-500' :
                                    index === 1 ? 'bg-gray-400' :
                                    index === 2 ? 'bg-yellow-600' : 'bg-gray-300'
                                  }`}>
                                    {index + 1}
                                  </div>
                                  <div>
                                    <h4 className="font-semibold text-gray-900">{candidate.name}</h4>
                                    <p className="text-sm text-gray-600">{candidate.description}</p>
                                  </div>
                                </div>
                                
                                <div className="text-right">
                                  <p className="text-lg font-bold text-gray-900">{candidate.votes}</p>
                                  <p className="text-sm text-gray-600">{percentage.toFixed(1)}%</p>
                                </div>
                              </div>
                            );
                          })}
                      </div>

                      {category.candidates.length === 0 && (
                        <p className="text-center text-gray-500 py-8">
                          Nenhum candidato nesta categoria
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Modals */}
      {showModal === 'category' && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-2xl p-8 max-w-md w-full">
            <h3 className="text-2xl font-bold text-gray-900 mb-6">
              {editingItem ? 'Editar Categoria' : 'Nova Categoria'}
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Nome</label>
                <input
                  type="text"
                  value={categoryForm.name}
                  onChange={(e) => setCategoryForm(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                  placeholder="Ex: Artista do Ano"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Descrição</label>
                <textarea
                  value={categoryForm.description}
                  onChange={(e) => setCategoryForm(prev => ({ ...prev, description: e.target.value }))}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                  rows={3}
                  placeholder="Descrição da categoria"
                />
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isActive"
                  checked={categoryForm.isActive}
                  onChange={(e) => setCategoryForm(prev => ({ ...prev, isActive: e.target.checked }))}
                  className="mr-2"
                />
                <label htmlFor="isActive" className="text-sm text-gray-700">Categoria ativa</label>
              </div>
            </div>
            
            <div className="flex space-x-4 mt-8">
              <button
                onClick={() => {
                  resetForms();
                  setShowModal(null);
                }}
                className="flex-1 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={editingItem ? handleUpdateCategory : handleAddCategory}
                className="flex-1 bg-gradient-to-r from-yellow-500 to-purple-600 text-white px-4 py-2 rounded-lg hover:shadow-lg transition-all duration-300"
              >
                {editingItem ? 'Atualizar' : 'Criar'}
              </button>
            </div>
          </div>
        </div>
      )}

      {showModal === 'candidate' && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-2xl p-8 max-w-md w-full">
            <h3 className="text-2xl font-bold text-gray-900 mb-6">
              {editingItem ? 'Editar Candidato' : 'Novo Candidato'}
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Nome</label>
                <input
                  type="text"
                  value={candidateForm.name}
                  onChange={(e) => setCandidateForm(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                  placeholder="Nome do candidato"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Descrição</label>
                <textarea
                  value={candidateForm.description}
                  onChange={(e) => setCandidateForm(prev => ({ ...prev, description: e.target.value }))}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                  rows={3}
                  placeholder="Descrição do candidato"
                />
              </div>
            </div>
            
            <div className="flex space-x-4 mt-8">
              <button
                onClick={() => {
                  resetForms();
                  setShowModal(null);
                }}
                className="flex-1 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={editingItem ? handleUpdateCandidate : handleAddCandidate}
                className="flex-1 bg-gradient-to-r from-yellow-500 to-purple-600 text-white px-4 py-2 rounded-lg hover:shadow-lg transition-all duration-300"
              >
                {editingItem ? 'Atualizar' : 'Adicionar'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Admin;