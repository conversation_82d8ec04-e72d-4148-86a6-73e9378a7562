import { Check<PERSON>ir<PERSON>, Clock, LogIn, Star, Users } from "lucide-react";
import React, { useState } from "react";
import { Link } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";
import { useVoting } from "../contexts/VotingContext";

const Voting: React.FC = () => {
  const { categories, submitVote, getUserVotes } = useVoting();
  const { user } = useAuth();
  const [selectedCandidates, setSelectedCandidates] = useState<
    Record<string, string>
  >({});
  const [votingStatus, setVotingStatus] = useState<
    Record<string, "voting" | "success" | "error">
  >({});
  const [showLoginPrompt, setShowLoginPrompt] = useState<string | null>(null);

  const userVotes = user ? getUserVotes(user.id) : [];
  const votedCategories = new Set(userVotes.map((vote) => vote.categoryId));

  const activeCategories = categories.filter((cat) => cat.isActive);

  const handleCandidateSelect = (categoryId: string, candidateId: string) => {
    if (votedCategories.has(categoryId)) return;

    setSelectedCandidates((prev) => ({
      ...prev,
      [categoryId]: candidateId,
    }));
  };

  const handleVoteAttempt = (categoryId: string) => {
    if (!user) {
      setShowLoginPrompt(categoryId);
      return;
    }
    handleVote(categoryId);
  };

  const handleVote = async (categoryId: string) => {
    const candidateId = selectedCandidates[categoryId];
    if (!candidateId || !user) return;

    setVotingStatus((prev) => ({ ...prev, [categoryId]: "voting" }));

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 1000));

    const success = submitVote(categoryId, candidateId);

    if (success) {
      setVotingStatus((prev) => ({ ...prev, [categoryId]: "success" }));
      // Remove from selected after successful vote
      setSelectedCandidates((prev) => {
        const newSelected = { ...prev };
        delete newSelected[categoryId];
        return newSelected;
      });
    } else {
      setVotingStatus((prev) => ({ ...prev, [categoryId]: "error" }));
    }

    // Clear status after 3 seconds
    setTimeout(() => {
      setVotingStatus((prev) => {
        const newStatus = { ...prev };
        delete newStatus[categoryId];
        return newStatus;
      });
    }, 3000);
  };

  if (activeCategories.length === 0) {
    return (
      <div className="min-h-screen bg-white py-12 px-4">
        <div className="max-w-4xl mx-auto">
          <div className="text-center py-20 bg-secondary-50 rounded-2xl shadow-sm p-8">
            <div className="inline-flex items-center justify-center p-4 bg-primary-500 rounded-full mb-8">
              <Clock className="h-16 w-16 text-white" />
            </div>
            <h2 className="text-3xl font-bold text-secondary-900 mb-4">
              Nenhuma Votação Ativa
            </h2>
            <p className="text-xl text-secondary-700">
              Não há categorias abertas para votação no momento. Volte em breve!
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white py-12">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-secondary-900 mb-4">
            Votações Ativas
          </h1>
          <p className="text-xl text-secondary-700 max-w-3xl mx-auto">
            {user
              ? "Escolha seus candidatos favoritos. Lembre-se: apenas um voto por categoria!"
              : "Conheça os candidatos e faça login para votar!"}
          </p>
        </div>

        {!user && (
          <div className="bg-primary-50 border border-primary-200 rounded-xl p-6 mb-8">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-primary-100 rounded-lg">
                  <LogIn className="h-6 w-6 text-primary-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-secondary-900">
                    Faça login para votar
                  </h3>
                  <p className="text-secondary-700 text-sm">
                    Você pode navegar e conhecer os candidatos, mas precisa
                    fazer login para votar.
                  </p>
                </div>
              </div>
              <Link
                to="/login"
                className="bg-primary-500 text-white px-6 py-2 rounded-lg font-medium hover:bg-primary-600 transition-colors text-center md:text-left"
              >
                Fazer Login
              </Link>
            </div>
          </div>
        )}

        <div className="space-y-12">
          {activeCategories.map((category) => {
            const hasVoted = votedCategories.has(category.id);
            const selectedCandidate = selectedCandidates[category.id];
            const status = votingStatus[category.id];
            const userVote = userVotes.find(
              (vote) => vote.categoryId === category.id
            );

            return (
              <div
                key={category.id}
                className="bg-white rounded-2xl shadow-md border border-secondary-100 overflow-hidden"
              >
                <div className="bg-primary-500 px-6 py-5">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
                    <div>
                      <h2 className="text-2xl font-bold text-white mb-1">
                        {category.name}
                      </h2>
                      <p className="text-white/90 text-sm">
                        {category.description}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2 text-white bg-primary-600/30 py-1 px-3 rounded-full md:self-start">
                      <Users className="h-4 w-4" />
                      <span className="font-medium text-sm">
                        {category.candidates.length} candidatos
                      </span>
                    </div>
                  </div>
                </div>

                <div className="p-6">
                  {hasVoted && (
                    <div className="mb-6 bg-primary-50 border border-primary-200 rounded-lg p-4">
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="h-5 w-5 text-primary-600" />
                        <p className="text-secondary-900 font-medium">
                          Você já votou nesta categoria!
                        </p>
                      </div>
                      {userVote && (
                        <div className="flex items-center mt-2 ml-7 px-3 py-1 bg-primary-100 rounded-full inline-block">
                          <p className="text-primary-700 text-sm">
                            Seu voto:{" "}
                            <span className="font-semibold">
                              {
                                category.candidates.find(
                                  (c) => c.id === userVote.candidateId
                                )?.name
                              }
                            </span>
                          </p>
                        </div>
                      )}
                    </div>
                  )}

                  <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                    {category.candidates.map((candidate) => {
                      const isSelected = selectedCandidate === candidate.id;
                      const isVotedFor = userVote?.candidateId === candidate.id;

                      return (
                        <div
                          key={candidate.id}
                          onClick={() =>
                            !hasVoted &&
                            handleCandidateSelect(category.id, candidate.id)
                          }
                          className={`relative p-6 rounded-xl transition-all duration-300 cursor-pointer ${
                            hasVoted
                              ? isVotedFor
                                ? "bg-primary-50 border-2 border-primary-300 shadow-md"
                                : "bg-white border border-secondary-100 opacity-60"
                              : isSelected
                              ? "bg-primary-50 border-2 border-primary-300 shadow-md"
                              : "bg-white border border-secondary-100 hover:border-primary-200 hover:shadow-sm"
                          } ${!hasVoted && "hover:-translate-y-1"}`}
                        >
                          {isSelected && !hasVoted && (
                            <div className="absolute top-3 right-3">
                              <div className="w-6 h-6 bg-primary-500 rounded-full flex items-center justify-center shadow-sm">
                                <CheckCircle className="h-4 w-4 text-white" />
                              </div>
                            </div>
                          )}

                          {isVotedFor && (
                            <div className="absolute top-3 right-3">
                              <div className="w-6 h-6 bg-primary-600 rounded-full flex items-center justify-center shadow-sm">
                                <CheckCircle className="h-4 w-4 text-white" />
                              </div>
                            </div>
                          )}

                          <div className="text-center">
                            <div className="w-20 h-20 bg-primary-500 rounded-full flex items-center justify-center mx-auto mb-4 shadow-sm">
                              <Star className="h-10 w-10 text-white" />
                            </div>
                            <h3 className="text-lg font-bold text-secondary-900 mb-2">
                              {candidate.name}
                            </h3>
                            <p className="text-secondary-700 text-sm">
                              {candidate.description}
                            </p>
                            {!user && (
                              <div className="mt-3 text-xs bg-primary-100 text-primary-700 rounded-full px-3 py-1 inline-block">
                                {candidate.votes} votos
                              </div>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>

                  {!hasVoted && (
                    <div className="text-center">
                      <button
                        onClick={() => handleVoteAttempt(category.id)}
                        disabled={!selectedCandidate || status === "voting"}
                        className="inline-flex items-center space-x-2 bg-primary-500 text-white px-8 py-3 rounded-full font-semibold hover:bg-primary-600 hover:shadow-md transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-primary-500 disabled:hover:shadow-none"
                      >
                        {status === "voting" ? (
                          <>
                            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                            <span>Enviando Voto...</span>
                          </>
                        ) : (
                          <>
                            <CheckCircle className="h-5 w-5" />
                            <span>
                              {user
                                ? "Confirmar Voto"
                                : "Fazer Login para Votar"}
                            </span>
                          </>
                        )}
                      </button>

                      {status === "success" && (
                        <div className="mt-4 text-primary-700 bg-primary-50 rounded-lg px-4 py-2 inline-flex items-center space-x-2">
                          <CheckCircle className="h-5 w-5" />
                          <span className="font-medium">
                            Voto registrado com sucesso!
                          </span>
                        </div>
                      )}

                      {status === "error" && (
                        <div className="mt-4 text-red-600 bg-red-50 rounded-lg px-4 py-2 inline-flex items-center space-x-2">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-5 w-5"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path
                              fillRule="evenodd"
                              d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                              clipRule="evenodd"
                            />
                          </svg>
                          <span className="font-medium">
                            Erro ao registrar voto. Tente novamente.
                          </span>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {user && (
          <div className="mt-12">
            <div className="bg-white rounded-xl p-6 shadow-md border border-secondary-100 max-w-lg mx-auto">
              <h3 className="text-lg font-semibold text-secondary-900 mb-2 text-center">
                Progresso das Votações
              </h3>
              <p className="text-secondary-700 text-center mb-4">
                Você votou em{" "}
                <span className="font-semibold text-primary-600">
                  {votedCategories.size}
                </span>{" "}
                de{" "}
                <span className="font-semibold text-secondary-900">
                  {activeCategories.length}
                </span>{" "}
                categorias ativas
              </p>
              <div className="w-full bg-secondary-100 rounded-full h-3 mb-1">
                <div
                  className="bg-primary-500 h-3 rounded-full transition-all duration-500"
                  style={{
                    width: `${
                      (votedCategories.size / activeCategories.length) * 100
                    }%`,
                  }}
                ></div>
              </div>
              <div className="flex justify-between text-xs text-secondary-500">
                <span>0%</span>
                <span>100%</span>
              </div>

              {votedCategories.size === activeCategories.length && (
                <div className="mt-4 text-center">
                  <div className="inline-flex items-center space-x-2 text-primary-700 bg-primary-50 px-4 py-2 rounded-full">
                    <CheckCircle className="h-5 w-5" />
                    <span className="font-medium">
                      Você votou em todas as categorias!
                    </span>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Login Prompt Modal */}
      {showLoginPrompt && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-2xl p-8 max-w-md w-full shadow-xl">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-md">
                <LogIn className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-secondary-900 mb-4">
                Login Necessário
              </h3>
              <p className="text-secondary-700 mb-8">
                Para votar, você precisa fazer login no sistema. É rápido e
                simples!
              </p>
              <div className="flex flex-col sm:flex-row gap-3">
                <button
                  onClick={() => setShowLoginPrompt(null)}
                  className="flex-1 px-4 py-3 border border-secondary-200 rounded-lg hover:bg-secondary-50 transition-colors font-medium text-secondary-700"
                >
                  Cancelar
                </button>
                <Link
                  to="/login"
                  className="flex-1 bg-primary-500 text-white px-4 py-3 rounded-lg hover:bg-primary-600 hover:shadow-md transition-all duration-300 text-center font-medium"
                >
                  Fazer Login
                </Link>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Voting;
