import { CheckCircle, KeyRound, Phone, Trophy, User } from "lucide-react";
import React, { useEffect, useState } from "react";
import { Navigate } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";

const Login: React.FC = () => {
  const [modoCadastro, setModoCadastro] = useState(false);
  const [nome, setNome] = useState("");
  const [telefone, setTelefone] = useState("");
  const [codigoSMS, setCodigoSMS] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [etapa, setEtapa] = useState<"telefone" | "codigo">("telefone");
  const [codigoEnviado, setCodigoEnviado] = useState(false);

  const { user, sendSMSCode, verifyCode, initRecaptcha } = useAuth();

  useEffect(() => {
    initRecaptcha("recaptcha-container");
  }, [initRecaptcha]);

  if (user) {
    return <Navigate to="/voting" replace />;
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    if (etapa === "telefone") {
      // Validar telefone
      if (!telefone.trim()) {
        setError("Digite seu número de telefone");
        setIsLoading(false);
        return;
      }

      if (modoCadastro && !nome.trim()) {
        setError("Digite seu nome");
        setIsLoading(false);
        return;
      }

      try {
        const success = await sendSMSCode(telefone);
        if (success) {
          setEtapa("codigo");
          setCodigoEnviado(true);
          setError("");
        } else {
          setError(
            "Erro ao enviar código SMS. Verifique o número e tente novamente."
          );
        }
      } catch (error: any) {
        console.error("Erro ao enviar SMS:", error);
        setError("Erro ao enviar código SMS. Tente novamente.");
      } finally {
        setIsLoading(false);
      }
    } else {
      // Verificar código SMS
      if (!codigoSMS.trim()) {
        setError("Digite o código recebido por SMS");
        setIsLoading(false);
        return;
      }

      try {
        const success = await verifyCode(
          codigoSMS,
          modoCadastro ? nome : undefined
        );
        if (!success) {
          setError("Código inválido. Verifique e tente novamente.");
        }
      } catch (error: any) {
        console.error("Erro ao verificar código:", error);
        setError("Código inválido. Tente novamente.");
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleReenviarCodigo = async () => {
    setIsLoading(true);
    setError("");

    try {
      const success = await sendSMSCode(telefone);
      if (success) {
        setError("");
        // Mostrar mensagem de sucesso temporariamente
      } else {
        setError("Erro ao reenviar código. Tente novamente.");
      }
    } catch (error) {
      setError("Erro ao reenviar código. Tente novamente.");
    } finally {
      setIsLoading(false);
    }
  };

  const voltarParaTelefone = () => {
    setEtapa("telefone");
    setCodigoSMS("");
    setCodigoEnviado(false);
    setError("");
  };

  return (
    <div className="min-h-screen bg-white flex flex-col md:flex-row">
      {/* Seção lateral */}
      <div className="hidden md:flex md:w-1/2 bg-primary-500 items-center justify-center">
        <div className="max-w-md p-8 text-white text-center">
          <div className="inline-flex items-center justify-center p-6 bg-white/10 backdrop-blur-sm rounded-full mb-6 shadow-lg">
            <Trophy className="h-16 w-16 text-white" />
          </div>
          <h1 className="text-4xl font-bold mb-4">Troféu Irmãos Dantas</h1>
          <p className="text-xl mb-8">
            Participe das votações e celebre os talentos que fazem a diferença
          </p>
          <div className="grid grid-cols-2 gap-4 text-left">
            <div className="flex items-start space-x-2">
              <CheckCircle className="h-6 w-6 text-white shrink-0" />
              <p className="text-sm">Vote em seus candidatos favoritos</p>
            </div>
            <div className="flex items-start space-x-2">
              <CheckCircle className="h-6 w-6 text-white shrink-0" />
              <p className="text-sm">Acompanhe os resultados em tempo real</p>
            </div>
            <div className="flex items-start space-x-2">
              <CheckCircle className="h-6 w-6 text-white shrink-0" />
              <p className="text-sm">Participe de múltiplas categorias</p>
            </div>
            <div className="flex items-start space-x-2">
              <CheckCircle className="h-6 w-6 text-white shrink-0" />
              <p className="text-sm">Interface simples e intuitiva</p>
            </div>
          </div>
        </div>
      </div>

      {/* Formulário */}
      <div className="flex flex-col justify-center items-center w-full md:w-1/2 py-12 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-md w-full space-y-10">
          <div className="text-center">
            <div className="md:hidden inline-flex items-center justify-center p-4 bg-primary-500 rounded-full mb-6 shadow-lg">
              <Trophy className="h-10 w-10 text-white" />
            </div>
            <h2 className="text-3xl font-extrabold text-secondary-900">
              {modoCadastro ? "Crie sua conta" : "Bem-vindo de volta"}
            </h2>
            <p className="mt-2 text-sm text-secondary-600">
              {modoCadastro
                ? "Preencha os dados abaixo para começar"
                : "Entre com seus dados para acessar o sistema"}
            </p>
          </div>

          <div className="flex justify-center border border-primary-200 rounded-lg overflow-hidden">
            <button
              className={`flex-1 py-3 px-4 font-medium text-sm transition-all ${
                !modoCadastro
                  ? "bg-primary-500 text-white"
                  : "bg-white text-secondary-700 hover:bg-primary-50"
              }`}
              onClick={() => {
                setModoCadastro(false);
                setError("");
              }}
              type="button"
            >
              Login
            </button>
            <button
              className={`flex-1 py-3 px-4 font-medium text-sm transition-all ${
                modoCadastro
                  ? "bg-primary-500 text-white"
                  : "bg-white text-secondary-700 hover:bg-primary-50"
              }`}
              onClick={() => {
                setModoCadastro(true);
                setError("");
              }}
              type="button"
            >
              Cadastro
            </button>
          </div>

          <div className="bg-white rounded-xl border border-secondary-100 p-6 shadow-sm">
            {/* Container do reCAPTCHA */}
            <div id="recaptcha-container" className="mb-4"></div>

            <form onSubmit={handleSubmit} className="space-y-6">
              {modoCadastro && etapa === "telefone" && (
                <div>
                  <label
                    htmlFor="nome"
                    className="block text-sm font-medium text-secondary-900 mb-1"
                  >
                    Nome Completo
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <User className="h-5 w-5 text-primary-500" />
                    </div>
                    <input
                      id="nome"
                      type="text"
                      value={nome}
                      onChange={(e) => setNome(e.target.value)}
                      className="block w-full pl-10 pr-3 py-3 bg-secondary-50 border border-secondary-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all"
                      placeholder="Digite seu nome completo"
                      required
                    />
                  </div>
                </div>
              )}

              {etapa === "telefone" && (
                <div>
                  <label
                    htmlFor="telefone"
                    className="block text-sm font-medium text-secondary-900 mb-1"
                  >
                    Número do Celular
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Phone className="h-5 w-5 text-primary-500" />
                    </div>
                    <input
                      id="telefone"
                      type="tel"
                      value={telefone}
                      onChange={(e) => setTelefone(e.target.value)}
                      className="block w-full pl-10 pr-3 py-3 bg-secondary-50 border border-secondary-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all"
                      placeholder="(11) 99999-9999"
                      required
                    />
                  </div>
                  <p className="mt-1 text-xs text-secondary-500">
                    Digite seu número com DDD. Ex: 11999999999
                  </p>
                </div>
              )}

              {etapa === "codigo" && (
                <div>
                  <label
                    htmlFor="codigo"
                    className="block text-sm font-medium text-secondary-900 mb-1"
                  >
                    Código de Verificação
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <KeyRound className="h-5 w-5 text-primary-500" />
                    </div>
                    <input
                      id="codigo"
                      type="text"
                      value={codigoSMS}
                      onChange={(e) => setCodigoSMS(e.target.value)}
                      className="block w-full pl-10 pr-3 py-3 bg-secondary-50 border border-secondary-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all"
                      placeholder="Digite o código de 6 dígitos"
                      maxLength={6}
                      required
                    />
                  </div>
                  <p className="mt-1 text-xs text-secondary-500">
                    Código enviado para {telefone}
                  </p>
                  <div className="mt-2 flex gap-2">
                    <button
                      type="button"
                      onClick={handleReenviarCodigo}
                      disabled={isLoading}
                      className="text-sm text-primary-600 hover:text-primary-700 disabled:opacity-50"
                    >
                      Reenviar código
                    </button>
                    <span className="text-secondary-400">•</span>
                    <button
                      type="button"
                      onClick={voltarParaTelefone}
                      className="text-sm text-secondary-600 hover:text-secondary-700"
                    >
                      Alterar número
                    </button>
                  </div>
                </div>
              )}

              {error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <p className="text-sm text-red-600">{error}</p>
                </div>
              )}

              <button
                type="submit"
                disabled={isLoading}
                className="w-full bg-primary-500 text-white py-3 px-4 rounded-lg font-medium hover:bg-primary-600 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <div className="flex items-center justify-center space-x-2">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                    <span>
                      {etapa === "telefone"
                        ? "Enviando código..."
                        : "Verificando..."}
                    </span>
                  </div>
                ) : etapa === "telefone" ? (
                  "Enviar código SMS"
                ) : (
                  "Verificar código"
                )}
              </button>
            </form>
          </div>
          <div className="text-center">
            <p className="text-xs text-secondary-500">
              Seus dados são seguros e usados apenas para validação dos votos.
              <br />
              Ao se cadastrar, você concorda com nossos{" "}
              <button
                type="button"
                className="text-primary-600 hover:text-primary-700 font-medium"
              >
                Termos de uso
              </button>
              .
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
