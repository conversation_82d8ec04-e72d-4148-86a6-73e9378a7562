import { ArrowR<PERSON>, Award, Star, Trophy, Users } from "lucide-react";
import React from "react";
import { <PERSON> } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";

const Home: React.FC = () => {
  const { user } = useAuth();

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative py-20 px-4 overflow-hidden">
        <div className="absolute inset-0 bg-primary-50"></div>
        <div className="relative max-w-7xl mx-auto text-center">
          <div className="inline-flex items-center justify-center p-4 bg-primary-500 rounded-full mb-8 shadow-lg">
            <Trophy className="h-16 w-16 text-white" />
          </div>

          <h1 className="text-5xl md:text-7xl font-bold mb-6 text-secondary-900">
            Troféu irmão dantas
          </h1>

          <p className="text-xl md:text-2xl text-secondary-800 mb-8 max-w-3xl mx-auto leading-relaxed">
            O maior evento de premiação do ano está aqui! Participe e vote nos
            seus artistas favoritos nas mais diversas categorias.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            {user ? (
              user.isAdmin ? (
                <Link
                  to="/admin"
                  className="group bg-primary-500 text-white px-8 py-4 rounded-full font-semibold text-lg hover:bg-primary-600 hover:shadow-lg transition-all duration-300 flex items-center space-x-2"
                >
                  <span>Painel Administrativo</span>
                  <ArrowRight className="h-5 w-5 group-hover:translate-x-1 transition-transform" />
                </Link>
              ) : (
                <Link
                  to="/voting"
                  className="group bg-primary-500 text-white px-8 py-4 rounded-full font-semibold text-lg hover:bg-primary-600 hover:shadow-lg transition-all duration-300 flex items-center space-x-2"
                >
                  <span>Votar Agora</span>
                  <ArrowRight className="h-5 w-5 group-hover:translate-x-1 transition-transform" />
                </Link>
              )
            ) : (
              <Link
                to="/login"
                className="group bg-primary-500 text-white px-8 py-4 rounded-full font-semibold text-lg hover:bg-primary-600 hover:shadow-lg transition-all duration-300 flex items-center space-x-2"
              >
                <span>Começar a Votar</span>
                <ArrowRight className="h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Link>
            )}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-secondary-900 mb-4">
              Como Funciona
            </h2>
            <p className="text-xl text-secondary-700 max-w-2xl mx-auto">
              Participe do processo de premiação de forma simples e transparente
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center group bg-white p-8 rounded-xl shadow-sm hover:shadow-md transition-all">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-primary-500 rounded-full mb-6 group-hover:shadow-md transition-all duration-300">
                <Users className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-secondary-900 mb-4">
                Cadastre-se
              </h3>
              <p className="text-secondary-700 leading-relaxed">
                Faça seu cadastro com nome e telefone para começar a participar
                das votações
              </p>
            </div>

            <div className="text-center group bg-white p-8 rounded-xl shadow-sm hover:shadow-md transition-all">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-primary-500 rounded-full mb-6 group-hover:shadow-md transition-all duration-300">
                <Star className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-secondary-900 mb-4">
                Vote
              </h3>
              <p className="text-secondary-700 leading-relaxed">
                Escolha seus candidatos favoritos em cada categoria ativa.
                Apenas um voto por categoria!
              </p>
            </div>

            <div className="text-center group bg-white p-8 rounded-xl shadow-sm hover:shadow-md transition-all">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-primary-500 rounded-full mb-6 group-hover:shadow-md transition-all duration-300">
                <Award className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-secondary-900 mb-4">
                Acompanhe
              </h3>
              <p className="text-secondary-700 leading-relaxed">
                Acompanhe a apuração dos votos e descubra os vencedores de cada
                categoria
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Timeline Section */}
      <section className="py-20 px-4 bg-secondary-50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-secondary-900 mb-4">
              Cronograma do Evento
            </h2>
            <p className="text-xl text-secondary-700 max-w-2xl mx-auto">
              Fique por dentro das datas importantes do evento
            </p>
          </div>

          <div className="space-y-12 relative before:absolute before:inset-0 before:h-full before:w-0.5 before:bg-primary-200 before:left-1/2 before:-translate-x-1/2 before:ml-px max-w-3xl mx-auto">
            <div className="relative flex items-center justify-between">
              <div className="w-5/12 pr-8 text-right">
                <h3 className="font-bold text-xl text-secondary-900 mb-2">
                  Abertura das Inscrições
                </h3>
                <p className="text-secondary-600">
                  As inscrições para participar do troféu começam oficialmente
                </p>
              </div>
              <div className="absolute left-1/2 -translate-x-1/2 flex items-center justify-center w-10 h-10 rounded-full bg-primary-500 text-white font-bold text-sm z-10">
                1
              </div>
              <div className="w-5/12 pl-8">
                <p className="text-lg font-semibold text-primary-600">
                  1º de Maio
                </p>
              </div>
            </div>

            <div className="relative flex items-center justify-between">
              <div className="w-5/12 pr-8 text-right">
                <p className="text-lg font-semibold text-primary-600">
                  15 de Maio
                </p>
              </div>
              <div className="absolute left-1/2 -translate-x-1/2 flex items-center justify-center w-10 h-10 rounded-full bg-primary-500 text-white font-bold text-sm z-10">
                2
              </div>
              <div className="w-5/12 pl-8">
                <h3 className="font-bold text-xl text-secondary-900 mb-2">
                  Início das Votações
                </h3>
                <p className="text-secondary-600">
                  O público pode começar a votar nos seus candidatos favoritos
                </p>
              </div>
            </div>

            <div className="relative flex items-center justify-between">
              <div className="w-5/12 pr-8 text-right">
                <h3 className="font-bold text-xl text-secondary-900 mb-2">
                  Encerramento das Votações
                </h3>
                <p className="text-secondary-600">
                  Último dia para registrar seus votos nas categorias
                </p>
              </div>
              <div className="absolute left-1/2 -translate-x-1/2 flex items-center justify-center w-10 h-10 rounded-full bg-primary-500 text-white font-bold text-sm z-10">
                3
              </div>
              <div className="w-5/12 pl-8">
                <p className="text-lg font-semibold text-primary-600">
                  30 de Junho
                </p>
              </div>
            </div>

            <div className="relative flex items-center justify-between">
              <div className="w-5/12 pr-8 text-right">
                <p className="text-lg font-semibold text-primary-600">
                  15 de Julho
                </p>
              </div>
              <div className="absolute left-1/2 -translate-x-1/2 flex items-center justify-center w-10 h-10 rounded-full bg-primary-500 text-white font-bold text-sm z-10">
                4
              </div>
              <div className="w-5/12 pl-8">
                <h3 className="font-bold text-xl text-secondary-900 mb-2">
                  Cerimônia de Premiação
                </h3>
                <p className="text-secondary-600">
                  Grande evento para anunciar e premiar os vencedores
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
