import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Category, Vote, VotingContextType, Candidate } from '../types';
import { useAuth } from './AuthContext';

const VotingContext = createContext<VotingContextType | undefined>(undefined);

export const useVoting = () => {
  const context = useContext(VotingContext);
  if (!context) {
    throw new Error('useVoting must be used within a VotingProvider');
  }
  return context;
};

interface VotingProviderProps {
  children: ReactNode;
}

const initialCategories: Category[] = [
  {
    id: '1',
    name: 'Artista do Ano',
    description: 'O melhor artista do ano em todas as categorias',
    isActive: true,
    candidates: [
      { id: '1', name: '<PERSON>', description: 'Can<PERSON> e compositor', votes: 0 },
      { id: '2', name: '<PERSON>', description: 'Cantora pop', votes: 0 },
      { id: '3', name: '<PERSON>', description: '<PERSON><PERSON>', votes: 0 }
    ]
  },
  {
    id: '2',
    name: 'Novo <PERSON>',
    description: 'Revelação do ano',
    isActive: true,
    candidates: [
      { id: '4', name: 'Ana Lima', description: 'Jovem cantora', votes: 0 },
      { id: '5', name: 'Carlos Mendes', description: 'Produtor musical', votes: 0 }
    ]
  }
];

export const VotingProvider: React.FC<VotingProviderProps> = ({ children }) => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [votes, setVotes] = useState<Vote[]>([]);
  const { user } = useAuth();

  useEffect(() => {
    const savedCategories = localStorage.getItem('trophyCategories');
    const savedVotes = localStorage.getItem('trophyVotes');

    if (savedCategories) {
      setCategories(JSON.parse(savedCategories));
    } else {
      setCategories(initialCategories);
      localStorage.setItem('trophyCategories', JSON.stringify(initialCategories));
    }

    if (savedVotes) {
      setVotes(JSON.parse(savedVotes));
    }
  }, []);

  const saveCategories = (newCategories: Category[]) => {
    setCategories(newCategories);
    localStorage.setItem('trophyCategories', JSON.stringify(newCategories));
  };

  const saveVotes = (newVotes: Vote[]) => {
    setVotes(newVotes);
    localStorage.setItem('trophyVotes', JSON.stringify(newVotes));
  };

  const addCategory = (category: Omit<Category, 'id'>) => {
    const newCategory: Category = {
      ...category,
      id: Date.now().toString()
    };
    saveCategories([...categories, newCategory]);
  };

  const updateCategory = (id: string, categoryUpdate: Partial<Category>) => {
    const updatedCategories = categories.map(cat => 
      cat.id === id ? { ...cat, ...categoryUpdate } : cat
    );
    saveCategories(updatedCategories);
  };

  const deleteCategory = (id: string) => {
    const filteredCategories = categories.filter(cat => cat.id !== id);
    saveCategories(filteredCategories);
  };

  const addCandidate = (categoryId: string, candidate: Omit<Candidate, 'id' | 'votes'>) => {
    const newCandidate: Candidate = {
      ...candidate,
      id: Date.now().toString(),
      votes: 0
    };

    const updatedCategories = categories.map(cat => 
      cat.id === categoryId 
        ? { ...cat, candidates: [...cat.candidates, newCandidate] }
        : cat
    );
    saveCategories(updatedCategories);
  };

  const updateCandidate = (categoryId: string, candidateId: string, candidateUpdate: Partial<Candidate>) => {
    const updatedCategories = categories.map(cat => 
      cat.id === categoryId 
        ? {
            ...cat,
            candidates: cat.candidates.map(candidate =>
              candidate.id === candidateId ? { ...candidate, ...candidateUpdate } : candidate
            )
          }
        : cat
    );
    saveCategories(updatedCategories);
  };

  const deleteCandidate = (categoryId: string, candidateId: string) => {
    const updatedCategories = categories.map(cat => 
      cat.id === categoryId 
        ? { ...cat, candidates: cat.candidates.filter(candidate => candidate.id !== candidateId) }
        : cat
    );
    saveCategories(updatedCategories);
  };

  const submitVote = (categoryId: string, candidateId: string): boolean => {
    if (!user) return false;

    // Check if user already voted in this category
    const existingVote = votes.find(vote => 
      vote.userId === user.id && vote.categoryId === categoryId
    );

    if (existingVote) return false;

    const newVote: Vote = {
      id: Date.now().toString(),
      userId: user.id,
      categoryId,
      candidateId,
      timestamp: new Date()
    };

    // Update vote count
    const updatedCategories = categories.map(cat => 
      cat.id === categoryId 
        ? {
            ...cat,
            candidates: cat.candidates.map(candidate =>
              candidate.id === candidateId 
                ? { ...candidate, votes: candidate.votes + 1 }
                : candidate
            )
          }
        : cat
    );

    saveCategories(updatedCategories);
    saveVotes([...votes, newVote]);
    return true;
  };

  const getUserVotes = (userId: string): Vote[] => {
    return votes.filter(vote => vote.userId === userId);
  };

  const getResults = (): Category[] => {
    return categories;
  };

  const exportResults = () => {
    const results = categories.map(category => ({
      category: category.name,
      candidates: category.candidates.map(candidate => ({
        name: candidate.name,
        votes: candidate.votes
      })).sort((a, b) => b.votes - a.votes)
    }));

    const dataStr = JSON.stringify(results, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    
    const exportFileDefaultName = `resultados-votacao-${new Date().toISOString().split('T')[0]}.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };

  return (
    <VotingContext.Provider value={{
      categories,
      votes,
      addCategory,
      updateCategory,
      deleteCategory,
      addCandidate,
      updateCandidate,
      deleteCandidate,
      submitVote,
      getUserVotes,
      getResults,
      exportResults
    }}>
      {children}
    </VotingContext.Provider>
  );
};