import { onAuthStateChanged } from "firebase/auth";
import React, {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useState,
} from "react";
import { auth } from "../config/firebase";
import {
  getUserProfile,
  logoutUser,
  updateUserProfile,
} from "../services/smsAuthService";
import { AuthContextType, User } from "../types";

const AuthContext = createContext<AuthContextType | undefined>(undefined);

const ADMIN_PASSWORD = "trophy2024admin";

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      if (firebaseUser) {
        try {
          const userProfile = await getUserProfile(firebaseUser.uid);
          if (userProfile) {
            const appUser: User = {
              id: firebaseUser.uid,
              uid: firebaseUser.uid,
              name: userProfile.displayName,
              email: userProfile.email,
              phone: "",
              isAdmin: userProfile.role === "admin",
            };
            setUser(appUser);
          }
        } catch (error) {
          console.error("Erro ao carregar perfil do usuário:", error);
        }
      } else {
        setUser(null);
      }
      setIsLoading(false);
    });

    return () => unsubscribe();
  }, []);

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      await loginUser(email, password);
      return true;
    } catch (error) {
      console.error("Erro no login:", error);
      return false;
    }
  };

  const register = async (
    email: string,
    password: string,
    name: string
  ): Promise<boolean> => {
    try {
      await registerUser(email, password, name);
      return true;
    } catch (error) {
      console.error("Erro no registro:", error);
      return false;
    }
  };

  const adminLogin = async (password: string): Promise<boolean> => {
    if (password !== ADMIN_PASSWORD) return false;

    // Para admin, você pode criar um usuário especial ou usar email/senha específicos
    // Por enquanto, mantendo a lógica original
    const adminUser: User = {
      id: "admin",
      name: "Administrador",
      email: "<EMAIL>",
      phone: "",
      isAdmin: true,
    };

    setUser(adminUser);
    return true;
  };

  const logout = async (): Promise<void> => {
    try {
      await logoutUser();
    } catch (error) {
      console.error("Erro no logout:", error);
    }
  };

  const updateProfile = async (updates: Partial<User>): Promise<boolean> => {
    try {
      if (user?.uid) {
        await updateUserProfile(user.uid, {
          displayName: updates.name,
          ...updates,
        });
        return true;
      }
      return false;
    } catch (error) {
      console.error("Erro ao atualizar perfil:", error);
      return false;
    }
  };

  const changeUserPassword = async (
    currentPassword: string,
    newPassword: string
  ): Promise<boolean> => {
    try {
      await changePassword(currentPassword, newPassword);
      return true;
    } catch (error) {
      console.error("Erro ao alterar senha:", error);
      return false;
    }
  };

  const sendPasswordResetEmail = async (email: string): Promise<boolean> => {
    try {
      await sendPasswordReset(email);
      return true;
    } catch (error) {
      console.error("Erro ao enviar email de redefinição:", error);
      return false;
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        login,
        register,
        adminLogin,
        logout,
        isLoading,
        updateProfile,
        changePassword: changeUserPassword,
        sendPasswordReset: sendPasswordResetEmail,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};
