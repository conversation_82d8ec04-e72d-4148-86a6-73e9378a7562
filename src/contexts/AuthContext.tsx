import { onAuthStateChanged } from "firebase/auth";
import React, {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useState,
} from "react";
import { auth } from "../config/firebase";
import {
  clearRecaptcha,
  getUserProfile,
  initializeR<PERSON>ptcha,
  logoutUser,
  sendSMSC<PERSON>,
  updateUserProfile,
  verifyCodeAndLogin,
} from "../services/smsAuthService";
import { AuthContextType, User } from "../types";

const AuthContext = createContext<AuthContextType | undefined>(undefined);

const ADMIN_PASSWORD = "trophy2024admin";

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      if (firebaseUser) {
        try {
          const userProfile = await getUserProfile(firebaseUser.uid);
          if (userProfile) {
            const appUser: User = {
              id: firebaseUser.uid,
              uid: firebaseUser.uid,
              name: userProfile.displayName,
              email: userProfile.email || "",
              phone: userProfile.phone,
              isAdmin: userProfile.role === "admin",
            };
            setUser(appUser);
          }
        } catch (error) {
          console.error("Erro ao carregar perfil do usuário:", error);
        }
      } else {
        setUser(null);
      }
      setIsLoading(false);
    });

    return () => unsubscribe();
  }, []);

  const sendSMS = async (phone: string): Promise<boolean> => {
    try {
      await sendSMSCode(phone);
      return true;
    } catch (error) {
      console.error("Erro ao enviar SMS:", error);
      return false;
    }
  };

  const verifyCode = async (code: string, name?: string): Promise<boolean> => {
    try {
      await verifyCodeAndLogin(code, name);
      return true;
    } catch (error) {
      console.error("Erro ao verificar código:", error);
      return false;
    }
  };

  const adminLogin = async (password: string): Promise<boolean> => {
    if (password !== ADMIN_PASSWORD) return false;

    const adminUser: User = {
      id: "admin",
      name: "Administrador",
      email: "<EMAIL>",
      phone: "",
      isAdmin: true,
    };

    setUser(adminUser);
    return true;
  };

  const logout = async (): Promise<void> => {
    try {
      await logoutUser();
      clearRecaptcha();
    } catch (error) {
      console.error("Erro no logout:", error);
    }
  };

  const updateProfile = async (updates: Partial<User>): Promise<boolean> => {
    try {
      if (user?.uid) {
        await updateUserProfile(user.uid, {
          displayName: updates.name,
          phone: updates.phone,
          ...updates,
        });
        return true;
      }
      return false;
    } catch (error) {
      console.error("Erro ao atualizar perfil:", error);
      return false;
    }
  };

  const initRecaptcha = (elementId: string): void => {
    try {
      initializeRecaptcha(elementId);
    } catch (error) {
      console.error("Erro ao inicializar reCAPTCHA:", error);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        sendSMSCode: sendSMS,
        verifyCode,
        adminLogin,
        logout,
        isLoading,
        updateProfile,
        initRecaptcha,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};
