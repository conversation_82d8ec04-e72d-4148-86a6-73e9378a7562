import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { User, AuthContextType } from '../types';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

const ADMIN_PASSWORD = 'trophy2024admin';

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const savedUser = localStorage.getItem('trophyUser');
    if (savedUser) {
      setUser(JSON.parse(savedUser));
    }
    setIsLoading(false);
  }, []);

  const login = async (phone: string, name: string): Promise<boolean> => {
    if (!phone || !name) return false;
    
    const newUser: User = {
      id: phone,
      name,
      phone,
      isAdmin: false
    };

    setUser(newUser);
    localStorage.setItem('trophyUser', JSON.stringify(newUser));
    return true;
  };

  const adminLogin = async (password: string): Promise<boolean> => {
    if (password !== ADMIN_PASSWORD) return false;

    const adminUser: User = {
      id: 'admin',
      name: 'Administrador',
      phone: '',
      isAdmin: true
    };

    setUser(adminUser);
    localStorage.setItem('trophyUser', JSON.stringify(adminUser));
    return true;
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('trophyUser');
  };

  return (
    <AuthContext.Provider value={{
      user,
      login,
      adminLogin,
      logout,
      isLoading
    }}>
      {children}
    </AuthContext.Provider>
  );
};