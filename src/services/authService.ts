import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  updateProfile,
  User,
  UserCredential,
  sendPasswordResetEmail,
  updatePassword,
  EmailAuthProvider,
  reauthenticateWithCredential
} from 'firebase/auth';
import { doc, setDoc, getDoc, updateDoc } from 'firebase/firestore';
import { auth, db } from '../config/firebase';

export interface UserProfile {
  uid: string;
  email: string;
  displayName: string;
  role: 'admin' | 'user';
  createdAt: Date;
  lastLogin: Date;
}

// Registrar novo usuário
export const registerUser = async (
  email: string,
  password: string,
  displayName: string
): Promise<UserCredential> => {
  try {
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;

    // Atualizar perfil do usuário
    await updateProfile(user, {
      displayName: displayName
    });

    // Criar documento do usuário no Firestore
    const userProfile: UserProfile = {
      uid: user.uid,
      email: user.email!,
      displayName: displayName,
      role: 'user', // Por padrão, novos usuários são 'user'
      createdAt: new Date(),
      lastLogin: new Date()
    };

    await setDoc(doc(db, 'users', user.uid), userProfile);

    return userCredential;
  } catch (error) {
    console.error('Erro ao registrar usuário:', error);
    throw error;
  }
};

// Fazer login
export const loginUser = async (email: string, password: string): Promise<UserCredential> => {
  try {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    
    // Atualizar último login
    await updateDoc(doc(db, 'users', userCredential.user.uid), {
      lastLogin: new Date()
    });

    return userCredential;
  } catch (error) {
    console.error('Erro ao fazer login:', error);
    throw error;
  }
};

// Fazer logout
export const logoutUser = async (): Promise<void> => {
  try {
    await signOut(auth);
  } catch (error) {
    console.error('Erro ao fazer logout:', error);
    throw error;
  }
};

// Obter perfil do usuário
export const getUserProfile = async (uid: string): Promise<UserProfile | null> => {
  try {
    const docRef = doc(db, 'users', uid);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      const data = docSnap.data();
      return {
        ...data,
        createdAt: data.createdAt.toDate(),
        lastLogin: data.lastLogin.toDate()
      } as UserProfile;
    } else {
      return null;
    }
  } catch (error) {
    console.error('Erro ao obter perfil do usuário:', error);
    throw error;
  }
};

// Atualizar perfil do usuário
export const updateUserProfile = async (
  uid: string,
  updates: Partial<UserProfile>
): Promise<void> => {
  try {
    const docRef = doc(db, 'users', uid);
    await updateDoc(docRef, updates);

    // Se o displayName foi atualizado, atualizar também no Auth
    if (updates.displayName && auth.currentUser) {
      await updateProfile(auth.currentUser, {
        displayName: updates.displayName
      });
    }
  } catch (error) {
    console.error('Erro ao atualizar perfil:', error);
    throw error;
  }
};

// Enviar email de redefinição de senha
export const sendPasswordReset = async (email: string): Promise<void> => {
  try {
    await sendPasswordResetEmail(auth, email);
  } catch (error) {
    console.error('Erro ao enviar email de redefinição:', error);
    throw error;
  }
};

// Alterar senha (requer reautenticação)
export const changePassword = async (
  currentPassword: string,
  newPassword: string
): Promise<void> => {
  try {
    const user = auth.currentUser;
    if (!user || !user.email) {
      throw new Error('Usuário não autenticado');
    }

    // Reautenticar usuário
    const credential = EmailAuthProvider.credential(user.email, currentPassword);
    await reauthenticateWithCredential(user, credential);

    // Atualizar senha
    await updatePassword(user, newPassword);
  } catch (error) {
    console.error('Erro ao alterar senha:', error);
    throw error;
  }
};

// Verificar se usuário é admin
export const isUserAdmin = async (uid: string): Promise<boolean> => {
  try {
    const userProfile = await getUserProfile(uid);
    return userProfile?.role === 'admin' || false;
  } catch (error) {
    console.error('Erro ao verificar se usuário é admin:', error);
    return false;
  }
};
