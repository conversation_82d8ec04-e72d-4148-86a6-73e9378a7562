import {
  ConfirmationResult,
  RecaptchaVerifier,
  signInWithPhoneNumber,
  signOut,
  updateProfile,
  User,
} from "firebase/auth";
import { doc, getDoc, setDoc, updateDoc } from "firebase/firestore";
import { auth, db } from "../config/firebase";

export interface UserProfile {
  uid: string;
  phone: string;
  displayName: string;
  role: "admin" | "user";
  email?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Variável global para armazenar o resultado da confirmação
let confirmationResult: ConfirmationResult | null = null;
let recaptchaVerifier: RecaptchaVerifier | null = null;

// Inicializar reCAPTCHA
export const initializeRecaptcha = (elementId: string): RecaptchaVerifier => {
  if (recaptchaVerifier) {
    recaptchaVerifier.clear();
  }

  recaptchaVerifier = new RecaptchaVerifier(auth, elementId, {
    size: "normal",
    callback: () => {
      console.log("reCAPTCHA solved");
    },
    "expired-callback": () => {
      console.log("reCAPTCHA expired");
    },
  });

  return recaptchaVerifier;
};

// Enviar código SMS
export const sendSMSCode = async (phoneNumber: string): Promise<boolean> => {
  try {
    if (!recaptchaVerifier) {
      throw new Error("reCAPTCHA not initialized");
    }

    // Formatar número para padrão internacional (+55)
    const formattedPhone = phoneNumber.startsWith("+55")
      ? phoneNumber
      : `+55${phoneNumber.replace(/\D/g, "")}`;

    confirmationResult = await signInWithPhoneNumber(
      auth,
      formattedPhone,
      recaptchaVerifier
    );
    return true;
  } catch (error: any) {
    console.error("Erro ao enviar SMS:", error);
    if (recaptchaVerifier) {
      recaptchaVerifier.clear();
      recaptchaVerifier = null;
    }
    throw error;
  }
};

// Verificar código SMS e fazer login/registro
export const verifyCodeAndLogin = async (
  code: string,
  name?: string
): Promise<{ user: User; isNewUser: boolean }> => {
  try {
    if (!confirmationResult) {
      throw new Error("No confirmation result available");
    }

    const result = await confirmationResult.confirm(code);
    const user = result.user;

    // Verificar se é um usuário novo
    const userDoc = await getDoc(doc(db, "users", user.uid));
    const isNewUser = !userDoc.exists();

    if (isNewUser && name) {
      // Criar perfil para novo usuário
      await createUserProfile(user, name);

      // Atualizar displayName no Firebase Auth
      await updateProfile(user, { displayName: name });
    } else if (!isNewUser) {
      // Atualizar último login
      await updateDoc(doc(db, "users", user.uid), {
        updatedAt: new Date(),
      });
    }

    return { user, isNewUser };
  } catch (error: any) {
    console.error("Erro ao verificar código:", error);
    throw error;
  }
};

// Criar perfil do usuário no Firestore
const createUserProfile = async (
  user: User,
  displayName: string
): Promise<void> => {
  const userProfile: UserProfile = {
    uid: user.uid,
    phone: user.phoneNumber || "",
    displayName,
    role: "user",
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  await setDoc(doc(db, "users", user.uid), userProfile);
};

// Obter perfil do usuário
export const getUserProfile = async (
  uid: string
): Promise<UserProfile | null> => {
  try {
    const userDoc = await getDoc(doc(db, "users", uid));
    if (userDoc.exists()) {
      return userDoc.data() as UserProfile;
    }
    return null;
  } catch (error) {
    console.error("Erro ao obter perfil:", error);
    return null;
  }
};

// Atualizar perfil do usuário
export const updateUserProfile = async (
  uid: string,
  updates: Partial<UserProfile>
): Promise<boolean> => {
  try {
    await updateDoc(doc(db, "users", uid), {
      ...updates,
      updatedAt: new Date(),
    });
    return true;
  } catch (error) {
    console.error("Erro ao atualizar perfil:", error);
    return false;
  }
};

// Logout
export const logoutUser = async (): Promise<boolean> => {
  try {
    await signOut(auth);

    // Limpar variáveis globais
    confirmationResult = null;
    if (recaptchaVerifier) {
      recaptchaVerifier.clear();
      recaptchaVerifier = null;
    }

    return true;
  } catch (error) {
    console.error("Erro ao fazer logout:", error);
    return false;
  }
};

// Verificar se usuário é admin
export const isUserAdmin = async (uid: string): Promise<boolean> => {
  try {
    const profile = await getUserProfile(uid);
    return profile?.role === "admin";
  } catch (error) {
    console.error("Erro ao verificar admin:", error);
    return false;
  }
};

// Limpar reCAPTCHA
export const clearRecaptcha = (): void => {
  if (recaptchaVerifier) {
    recaptchaVerifier.clear();
    recaptchaVerifier = null;
  }
  confirmationResult = null;
};
