# Utiliza imagem oficial do Node.js LTS
FROM node:20-alpine

# Define diretório de trabalho
WORKDIR /app

# Copia arquivos de dependências
COPY package.json package-lock.json ./

# Instala todas as dependências
RUN npm ci

# Copia todos os arquivos necessários para o build
COPY . .

# Define variáveis de ambiente
ENV NODE_ENV=production

# Compila o projeto React para produção
RUN npm run build

# Instala um servidor HTTP estático
RUN npm install -g serve

# Expõe a porta padrão
EXPOSE 80

# Comando para iniciar o servidor
CMD ["serve", "-s", "dist", "-l", "80"]
