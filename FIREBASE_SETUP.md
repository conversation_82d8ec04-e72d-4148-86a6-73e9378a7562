# Configuração do Firebase para o Troféu Irmãos Dantas

Este guia irá ajudá-lo a configurar o Firebase para autenticação no projeto.

## Passo 1: Criar um projeto no Firebase

1. Acesse o [Console do Firebase](https://console.firebase.google.com/)
2. Clique em "Adicionar projeto"
3. Digite o nome do projeto (ex: "trofeu-irmaos-dantas")
4. Desabilite o Google Analytics (opcional)
5. Clique em "Criar projeto"

## Passo 2: Configurar Authentication

1. No console do Firebase, vá para "Authentication" no menu lateral
2. Clique na aba "Sign-in method"
3. Habilite o provedor "Email/Password"
4. Clique em "Salvar"

## Passo 3: Configurar Firestore Database

1. No console do Firebase, vá para "Firestore Database"
2. Clique em "Criar banco de dados"
3. Escolha "Iniciar no modo de teste" (por enquanto)
4. Selecione uma localização (ex: southamerica-east1)
5. Clique em "Concluído"

## Passo 4: Obter as credenciais do projeto

1. No console do Firebase, clique no ícone de engrenagem e vá para "Configurações do projeto"
2. Na aba "Geral", role para baixo até "Seus aplicativos"
3. Clique no ícone "</>" para adicionar um app da web
4. Digite um nome para o app (ex: "trofeu-web")
5. NÃO marque "Configurar também o Firebase Hosting"
6. Clique em "Registrar app"
7. Copie as configurações mostradas

## Passo 5: Configurar as variáveis de ambiente

1. Abra o arquivo `.env` na raiz do projeto
2. Substitua os valores pelas suas credenciais do Firebase:

```env
VITE_FIREBASE_API_KEY=sua-api-key-aqui
VITE_FIREBASE_AUTH_DOMAIN=seu-project-id.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=seu-project-id
VITE_FIREBASE_STORAGE_BUCKET=seu-project-id.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=seu-sender-id
VITE_FIREBASE_APP_ID=seu-app-id
```

## Passo 6: Configurar regras de segurança do Firestore

1. No console do Firebase, vá para "Firestore Database"
2. Clique na aba "Regras"
3. Substitua as regras padrão por:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Permitir leitura e escrita apenas para usuários autenticados
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Permitir leitura de categorias e candidatos para usuários autenticados
    match /categories/{document=**} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // Permitir leitura e escrita de votos apenas para o próprio usuário
    match /votes/{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

4. Clique em "Publicar"

## Passo 7: Criar um usuário administrador

Após configurar tudo, você pode:

1. Registrar um usuário normal através da interface
2. No console do Firebase, ir para "Firestore Database"
3. Encontrar o documento do usuário em `users/{uid}`
4. Editar o campo `role` de `user` para `admin`

## Testando a configuração

1. Execute o projeto: `npm run dev`
2. Acesse a página de login
3. Tente criar uma conta com email e senha
4. Verifique se o usuário foi criado no Firebase Authentication
5. Verifique se o documento do usuário foi criado no Firestore

## Funcionalidades implementadas

- ✅ Registro de usuários com email e senha
- ✅ Login com email e senha
- ✅ Logout
- ✅ Persistência de sessão
- ✅ Perfis de usuário no Firestore
- ✅ Diferenciação entre usuários normais e administradores
- ✅ Atualização de perfil
- ✅ Alteração de senha
- ✅ Redefinição de senha por email

## Próximos passos

- Configurar regras de segurança mais específicas
- Implementar verificação de email
- Adicionar autenticação com Google/Facebook (opcional)
- Configurar backup automático do Firestore
